# Table Seating Arrangement Export Functionality

## Overview
Added PDF and Excel export functionality to the Wedding Admin Dashboard's Table Seating Arrangement feature.

## Features Added

### 1. PDF Export
- **File Name**: `wedding-table-seating-arrangement.pdf`
- **Content**:
  - Title and generation date
  - Each table with assigned guests in a formatted table
  - Guest details: Seat number, Title, Name, Surname, Type (Regular/Late Guest)
  - Summary section with statistics
  - Professional formatting with custom colors

### 2. Excel Export
- **File Name**: `wedding-table-seating-arrangement.xlsx`
- **Multiple Sheets**:
  - **Summary Sheet**: Overview with statistics and table details
  - **Detailed Seating Sheet**: All guests with their table assignments
  - **Individual Table Sheets**: Separate sheet for each table with guests

## How to Use

1. Navigate to the "Table Seating" section in the admin dashboard
2. Assign guests to tables as needed
3. Click either:
   - **📄 Download PDF** - for a formatted PDF document
   - **📊 Download Excel** - for a comprehensive Excel workbook

## Technical Implementation

### Dependencies Added
- `jspdf` - PDF generation
- `jspdf-autotable` - Table formatting in PDFs
- `xlsx` - Excel file generation

### Key Features
- **Error Handling**: Graceful handling of empty tables
- **Professional Formatting**: Custom colors and styling
- **Comprehensive Data**: Includes both regular and late guests
- **Multiple Formats**: Both PDF and Excel for different use cases
- **Responsive Design**: Export buttons styled to match the existing UI

## File Structure
- PDF exports include tables with guests, formatted professionally
- Excel exports provide multiple views of the data for different purposes
- Both formats include summary statistics and generation timestamps

## Usage Scenarios
- **PDF**: Perfect for printing and sharing with vendors
- **Excel**: Ideal for further data manipulation and analysis
- **Both**: Comprehensive backup of seating arrangements

The export functionality automatically handles:
- Empty tables (shows appropriate message)
- Guest type identification (Regular vs Late Guest)
- Professional formatting and styling
- Multiple data views in Excel format
